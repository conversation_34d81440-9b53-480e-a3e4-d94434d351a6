// import LocaleSwitcher from '@/components/shared/locale-switcher';
import { LocaleToggle } from '@/components/core/LocaleToggle'
import Article from '@/components/shared/Article'

import ThemeSwitch from '@/components/shared/ThemeSwitch'
import { apiService } from '@/services'
import { IArticle } from '@/types'

export default async function Home() {
  const home = await apiService({
    path: 'home',
  })

  console.log(home?.data?.articles)
  return (
    <main className="flex min-h-screen flex-col items-center justify-between p-8">
      {/* <LocaleSwitcher /> */}
      <ThemeSwitch />
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {home?.data?.articles.map((article: IArticle) => (
          <Article key={article.id} date={article.date} title={article.title} image={article.image} />
        ))}
      </div>
      <LocaleToggle />
    </main>
  )
}
