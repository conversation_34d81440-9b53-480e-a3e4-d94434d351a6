// import LocaleSwitcher from '@/components/shared/locale-switcher';
import { LocaleToggle } from '@/components/core/LocaleToggle'
import Post from '@/components/shared/Post'
import ThemeSwitch from '@/components/shared/ThemeSwitch'
import { apiService } from '@/services'

// import { getTranslations } from 'next-intl/server';

export default async function Home() {
  const home = await apiService({
    path: 'home',
  })

  console.log(home)
  return (
    <main className="flex min-h-screen flex-col items-center justify-between">
      {/* <LocaleSwitcher /> */}
      <ThemeSwitch />
      <Post />
      <LocaleToggle />
    </main>
  )
}
