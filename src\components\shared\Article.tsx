import Image from 'next/image'
import Link from 'next/link'
import { ChevronRight } from 'lucide-react'
import { IArticle } from '@/types'
import { useTranslations } from 'next-intl'

const Article = ({ date, title, image, id, media_type }: IArticle) => {
  const t = useTranslations()
  const shareUrl = encodeURIComponent('https://example.com')
  const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${shareUrl}`
  const xUrl = `https://twitter.com/intent/tweet?url=${shareUrl}`
   const getYoutubeEmbed = (url: string) => {
    const videoId = new URL(url).searchParams.get("v");
    return `https://www.youtube.com/embed/${videoId}`;
  };
  return (
    <div className="bg-white rounded-[24px] overflow-hidden">
      <div className="relative w-full h-[250px]">
        <Link href={`/article/${id}`}>
          {media_type === 'link' ? (
              <iframe
          src={getYoutubeEmbed(link)}
          className="w-full h-full rounded-xl"
          title="YouTube video"
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
        />
          ) : (
            <Image src={image} alt="Article Image" width={370} height={250} />
          )}
        </Link>
      </div>
      <div className="p-8">
        <div className="flex justify-between items-center">
          <p className="text-xl font-bold text-gray-01">{date}</p>
          <div className="flex gap-4 items-center h-fit">
            <Link href={xUrl}>
              <div className="relative w-8 h-8">
                <Image src={require('/public/icons/x.svg')} alt="X icon" fill className="object-contain" />
              </div>
            </Link>
            <Link href={'instagramUrl'}>
              <div className="relative w-8 h-8">
                <Image src={require('/public/icons/insta.svg')} alt="Instagram icon" fill className="object-contain" />
              </div>
            </Link>

            <Link href={facebookUrl}>
              <div className="relative w-8 h-8">
                <Image
                  src={require('/public/icons/facebook.svg')}
                  alt="Facebook icon"
                  fill
                  className="object-contain"
                />
              </div>
            </Link>
          </div>
        </div>
        <h3 className="text-primary-03 text-[28px] py-6">{title}</h3>
        <Link href="#" className="text-primary-02 font-bold text-2xl flex items-center">
          <ChevronRight />
          {t('articles.read_more')}
        </Link>
      </div>
    </div>
  )
}

export default Article
