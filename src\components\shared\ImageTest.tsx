'use client'

import Image from 'next/image'

const ImageTest = () => {
  return (
    <div className="p-8 space-y-4">
      <h2 className="text-2xl font-bold">Image Test Component</h2>
      
      {/* Test main image */}
      <div className="border p-4 rounded">
        <h3 className="text-lg font-semibold mb-2">Main Image Test:</h3>
        <div className="relative w-[370px] h-[250px] border">
          <Image
            src="/images/img.png"
            alt="Test main image"
            fill
            className="object-cover"
            onError={(e) => {
              console.error('Failed to load main image:', e)
            }}
            onLoad={() => {
              console.log('Main image loaded successfully')
            }}
          />
        </div>
      </div>

      {/* Test icons */}
      <div className="border p-4 rounded">
        <h3 className="text-lg font-semibold mb-2">Icons Test:</h3>
        <div className="flex gap-4">
          <div className="text-center">
            <div className="relative w-8 h-8 border mb-2">
              <Image
                src="/icons/facebook.svg"
                alt="Facebook icon"
                fill
                className="object-contain"
                onError={(e) => {
                  console.error('Failed to load Facebook icon:', e)
                }}
                onLoad={() => {
                  console.log('Facebook icon loaded successfully')
                }}
              />
            </div>
            <p className="text-sm">Facebook</p>
          </div>
          
          <div className="text-center">
            <div className="relative w-8 h-8 border mb-2">
              <Image
                src="/icons/insta.svg"
                alt="Instagram icon"
                fill
                className="object-contain"
                onError={(e) => {
                  console.error('Failed to load Instagram icon:', e)
                }}
                onLoad={() => {
                  console.log('Instagram icon loaded successfully')
                }}
              />
            </div>
            <p className="text-sm">Instagram</p>
          </div>
          
          <div className="text-center">
            <div className="relative w-8 h-8 border mb-2">
              <Image
                src="/icons/x.svg"
                alt="X icon"
                fill
                className="object-contain"
                onError={(e) => {
                  console.error('Failed to load X icon:', e)
                }}
                onLoad={() => {
                  console.log('X icon loaded successfully')
                }}
              />
            </div>
            <p className="text-sm">X (Twitter)</p>
          </div>
        </div>
      </div>

      {/* Alternative using regular img tag for comparison */}
      <div className="border p-4 rounded">
        <h3 className="text-lg font-semibold mb-2">Regular img tag test:</h3>
        <div className="flex gap-4">
          <img src="/images/img.png" alt="Regular img test" className="w-20 h-20 object-cover border" />
          <img src="/icons/facebook.svg" alt="Facebook regular" className="w-8 h-8 border" />
          <img src="/icons/insta.svg" alt="Instagram regular" className="w-8 h-8 border" />
          <img src="/icons/x.svg" alt="X regular" className="w-8 h-8 border" />
        </div>
      </div>
    </div>
  )
}

export default ImageTest
