import Image from 'next/image'
import Link from 'next/link'
import { ChevronRight } from 'lucide-react'

const Post = () => {
  return (
    <div className="bg-white rounded-[24px] overflow-hidden">
      <Image src="/images/img.png" alt="post" width={370} height={250} />
      <div className="p-8">
        <div className="flex justify-between items-center">
          <p className="text-xl font-bold text-gray-01">date</p>
          <div className="flex gap-4 items-center h-fit">
            <Link href="#">
              <Image className="h-[32px]" src="/icons/facebook.svg" alt="Facebook icon" width={32} height={32} />
            </Link>
            <Link href="#">
              <Image src="/icons/insta.svg" alt="Instagram icon" width={32} height={32} />
            </Link>
            <Link href="#">
              <Image src="/icons/x.svg" alt="X icon" width={32} height={32} />
            </Link>
          </div>
        </div>
        <h3 className="text-primary-03 text-[28px]">title</h3>
        <p className="text-primary-02 font-bold text-2xl">
          more
          <span>
            <ChevronRight />
          </span>
        </p>
      </div>
    </div>
  )
}

export default Post
